import { useMemo } from 'react';
import {
  useScreenDimensions,
  getDeviceType,
  responsiveFontSize,
  responsiveSpacing,
  getGridColumns,
  getResponsiveWidth,
  responsiveStyle,
  getModalDimensions,
  getNavigationConfig,
  getCardDimensions,
  spacing,
  BREAKPOINTS,
  DeviceType,
} from '../utils/responsive';

export interface ResponsiveHookReturn {
  // Screen information
  screenWidth: number;
  screenHeight: number;
  deviceType: DeviceType;
  isLandscape: boolean;
  isPortrait: boolean;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  
  // Responsive functions
  fontSize: (size: number) => number;
  spacing: (size: number) => number;
  
  // Layout helpers
  gridColumns: number;
  getWidth: (columns: number, totalColumns?: number) => string;
  
  // Style helpers
  style: <T extends Record<string, any>>(styles: {
    mobile?: T;
    tablet?: T;
    desktop?: T;
    default: T;
  }) => T;
  
  // Component dimensions
  modal: ReturnType<typeof getModalDimensions>;
  navigation: ReturnType<typeof getNavigationConfig>;
  card: ReturnType<typeof getCardDimensions>;
  
  // Predefined spacing
  spacingPresets: typeof spacing;
  
  // Breakpoint checks
  isAbove: (breakpoint: keyof typeof BREAKPOINTS) => boolean;
  isBelow: (breakpoint: keyof typeof BREAKPOINTS) => boolean;
  isBetween: (min: keyof typeof BREAKPOINTS, max: keyof typeof BREAKPOINTS) => boolean;
}

export const useResponsive = (): ResponsiveHookReturn => {
  const dimensions = useScreenDimensions();
  
  const responsiveValues = useMemo(() => {
    const deviceType = dimensions.deviceType;
    
    return {
      // Screen information
      screenWidth: dimensions.width,
      screenHeight: dimensions.height,
      deviceType,
      isLandscape: dimensions.isLandscape,
      isPortrait: dimensions.isPortrait,
      isMobile: deviceType === 'mobile',
      isTablet: deviceType === 'tablet',
      isDesktop: deviceType === 'desktop',
      
      // Responsive functions
      fontSize: responsiveFontSize,
      spacing: responsiveSpacing,
      
      // Layout helpers
      gridColumns: getGridColumns(deviceType),
      getWidth: getResponsiveWidth,
      
      // Style helpers
      style: responsiveStyle,
      
      // Component dimensions
      modal: getModalDimensions(),
      navigation: getNavigationConfig(),
      card: getCardDimensions(),
      
      // Predefined spacing
      spacingPresets: spacing,
      
      // Breakpoint checks
      isAbove: (breakpoint: keyof typeof BREAKPOINTS) => {
        return dimensions.width >= BREAKPOINTS[breakpoint];
      },
      
      isBelow: (breakpoint: keyof typeof BREAKPOINTS) => {
        return dimensions.width < BREAKPOINTS[breakpoint];
      },
      
      isBetween: (min: keyof typeof BREAKPOINTS, max: keyof typeof BREAKPOINTS) => {
        return dimensions.width >= BREAKPOINTS[min] && dimensions.width < BREAKPOINTS[max];
      },
    };
  }, [dimensions]);
  
  return responsiveValues;
};

// Convenience hooks for specific use cases
export const useResponsiveLayout = () => {
  const responsive = useResponsive();
  
  return {
    columns: responsive.gridColumns,
    cardWidth: responsive.getWidth(1),
    halfWidth: responsive.getWidth(1, 2),
    thirdWidth: responsive.getWidth(1, 3),
    quarterWidth: responsive.getWidth(1, 4),
    twoThirds: responsive.getWidth(2, 3),
    threeQuarters: responsive.getWidth(3, 4),
    fullWidth: '100%',
  };
};

export const useResponsiveSpacing = () => {
  const responsive = useResponsive();
  
  return {
    xs: responsive.spacingPresets.xs,
    sm: responsive.spacingPresets.sm,
    md: responsive.spacingPresets.md,
    lg: responsive.spacingPresets.lg,
    xl: responsive.spacingPresets.xl,
    xxl: responsive.spacingPresets.xxl,
    custom: responsive.spacing,
  };
};

export const useResponsiveTypography = () => {
  const responsive = useResponsive();
  
  return {
    h1: responsive.fontSize(32),
    h2: responsive.fontSize(28),
    h3: responsive.fontSize(24),
    h4: responsive.fontSize(20),
    h5: responsive.fontSize(18),
    h6: responsive.fontSize(16),
    body: responsive.fontSize(16),
    bodySmall: responsive.fontSize(14),
    caption: responsive.fontSize(12),
    overline: responsive.fontSize(10),
    custom: responsive.fontSize,
  };
};

export const useResponsiveNavigation = () => {
  const responsive = useResponsive();
  
  return {
    config: responsive.navigation,
    shouldShowBottomTabs: responsive.navigation.showBottomTabs,
    shouldShowSideNav: responsive.navigation.showSideNavigation,
    tabBarHeight: responsive.navigation.tabBarHeight,
    sideNavWidth: responsive.navigation.sideNavWidth,
  };
};

export const useResponsiveModal = () => {
  const responsive = useResponsive();
  
  return {
    dimensions: responsive.modal,
    isFullScreen: responsive.isMobile,
    shouldUseSheet: responsive.isTablet || responsive.isDesktop,
    presentationStyle: responsive.isMobile ? 'fullScreen' : 'pageSheet',
  };
};

// Helper hook for responsive grid layouts
export const useResponsiveGrid = (itemsPerRow?: { mobile?: number; tablet?: number; desktop?: number }) => {
  const responsive = useResponsive();
  
  const columns = useMemo(() => {
    if (itemsPerRow) {
      if (responsive.isDesktop && itemsPerRow.desktop) return itemsPerRow.desktop;
      if (responsive.isTablet && itemsPerRow.tablet) return itemsPerRow.tablet;
      if (responsive.isMobile && itemsPerRow.mobile) return itemsPerRow.mobile;
    }
    return responsive.gridColumns;
  }, [responsive.deviceType, itemsPerRow]);
  
  return {
    columns,
    itemWidth: responsive.getWidth(1, columns),
    gap: responsive.spacingPresets.sm,
    containerPadding: responsive.spacingPresets.md,
  };
};

export default useResponsive;
