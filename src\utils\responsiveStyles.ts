import { StyleSheet, ViewStyle, TextStyle } from 'react-native';
import {
  responsiveFontSize,
  responsiveSpacing,
  getDeviceType,
  spacing,
  getCardDimensions,
  getModalDimensions,
  platformValue,
} from './responsive';

// Common responsive text styles
export const responsiveText = StyleSheet.create({
  h1: {
    fontSize: responsiveFontSize(32),
    fontWeight: 'bold',
    lineHeight: responsiveFontSize(40),
    marginBottom: spacing.md,
  } as TextStyle,
  
  h2: {
    fontSize: responsiveFontSize(28),
    fontWeight: 'bold',
    lineHeight: responsiveFontSize(36),
    marginBottom: spacing.md,
  } as TextStyle,
  
  h3: {
    fontSize: responsiveFontSize(24),
    fontWeight: 'bold',
    lineHeight: responsiveFontSize(32),
    marginBottom: spacing.sm,
  } as TextStyle,
  
  h4: {
    fontSize: responsiveFontSize(20),
    fontWeight: '600',
    lineHeight: responsiveFontSize(28),
    marginBottom: spacing.sm,
  } as TextStyle,
  
  h5: {
    fontSize: responsiveFontSize(18),
    fontWeight: '600',
    lineHeight: responsiveFontSize(24),
    marginBottom: spacing.sm,
  } as TextStyle,
  
  h6: {
    fontSize: responsiveFontSize(16),
    fontWeight: '600',
    lineHeight: responsiveFontSize(22),
    marginBottom: spacing.xs,
  } as TextStyle,
  
  body: {
    fontSize: responsiveFontSize(16),
    lineHeight: responsiveFontSize(24),
    fontWeight: '400',
  } as TextStyle,
  
  bodySmall: {
    fontSize: responsiveFontSize(14),
    lineHeight: responsiveFontSize(20),
    fontWeight: '400',
  } as TextStyle,
  
  caption: {
    fontSize: responsiveFontSize(12),
    lineHeight: responsiveFontSize(16),
    fontWeight: '400',
  } as TextStyle,
  
  overline: {
    fontSize: responsiveFontSize(10),
    lineHeight: responsiveFontSize(14),
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 1.5,
  } as TextStyle,
});

// Common responsive container styles
export const responsiveContainers = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  } as ViewStyle,
  
  screenPadded: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    paddingHorizontal: responsiveSpacing(16),
  } as ViewStyle,
  
  card: {
    backgroundColor: 'white',
    borderRadius: responsiveSpacing(12),
    padding: responsiveSpacing(16),
    marginBottom: responsiveSpacing(12),
    ...platformValue({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
      },
      android: {
        elevation: 5,
      },
      web: {
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      },
      default: {},
    }),
  } as ViewStyle,
  
  cardCompact: {
    backgroundColor: 'white',
    borderRadius: responsiveSpacing(8),
    padding: responsiveSpacing(12),
    marginBottom: responsiveSpacing(8),
    ...platformValue({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 1px 4px rgba(0,0,0,0.05)',
      },
      default: {},
    }),
  } as ViewStyle,
  
  section: {
    backgroundColor: 'white',
    marginHorizontal: responsiveSpacing(16),
    marginVertical: responsiveSpacing(8),
    borderRadius: responsiveSpacing(12),
    padding: responsiveSpacing(20),
  } as ViewStyle,
  
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,
  
  column: {
    flexDirection: 'column',
  } as ViewStyle,
  
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,
  
  spaceBetween: {
    justifyContent: 'space-between',
  } as ViewStyle,
  
  spaceAround: {
    justifyContent: 'space-around',
  } as ViewStyle,
  
  spaceEvenly: {
    justifyContent: 'space-evenly',
  } as ViewStyle,
});

// Responsive grid styles
export const createResponsiveGrid = (columns: number = 2, gap: number = spacing.sm) => {
  const itemWidth = `${(100 - ((columns - 1) * gap * 100) / responsiveSpacing(16)) / columns}%`;
  
  return StyleSheet.create({
    container: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      paddingHorizontal: responsiveSpacing(16),
      gap: gap,
    } as ViewStyle,
    
    item: {
      width: itemWidth,
      minWidth: itemWidth,
    } as ViewStyle,
    
    itemHalf: {
      width: '48%',
      minWidth: '48%',
    } as ViewStyle,
    
    itemThird: {
      width: '31%',
      minWidth: '31%',
    } as ViewStyle,
    
    itemQuarter: {
      width: '23%',
      minWidth: '23%',
    } as ViewStyle,
    
    itemFull: {
      width: '100%',
    } as ViewStyle,
  });
};

// Responsive spacing utilities
export const responsiveSpacingStyles = StyleSheet.create({
  // Margins
  marginXS: { margin: spacing.xs } as ViewStyle,
  marginSM: { margin: spacing.sm } as ViewStyle,
  marginMD: { margin: spacing.md } as ViewStyle,
  marginLG: { margin: spacing.lg } as ViewStyle,
  marginXL: { margin: spacing.xl } as ViewStyle,
  marginXXL: { margin: spacing.xxl } as ViewStyle,
  
  // Horizontal margins
  marginHorizontalXS: { marginHorizontal: spacing.xs } as ViewStyle,
  marginHorizontalSM: { marginHorizontal: spacing.sm } as ViewStyle,
  marginHorizontalMD: { marginHorizontal: spacing.md } as ViewStyle,
  marginHorizontalLG: { marginHorizontal: spacing.lg } as ViewStyle,
  marginHorizontalXL: { marginHorizontal: spacing.xl } as ViewStyle,
  
  // Vertical margins
  marginVerticalXS: { marginVertical: spacing.xs } as ViewStyle,
  marginVerticalSM: { marginVertical: spacing.sm } as ViewStyle,
  marginVerticalMD: { marginVertical: spacing.md } as ViewStyle,
  marginVerticalLG: { marginVertical: spacing.lg } as ViewStyle,
  marginVerticalXL: { marginVertical: spacing.xl } as ViewStyle,
  
  // Padding
  paddingXS: { padding: spacing.xs } as ViewStyle,
  paddingSM: { padding: spacing.sm } as ViewStyle,
  paddingMD: { padding: spacing.md } as ViewStyle,
  paddingLG: { padding: spacing.lg } as ViewStyle,
  paddingXL: { padding: spacing.xl } as ViewStyle,
  paddingXXL: { padding: spacing.xxl } as ViewStyle,
  
  // Horizontal padding
  paddingHorizontalXS: { paddingHorizontal: spacing.xs } as ViewStyle,
  paddingHorizontalSM: { paddingHorizontal: spacing.sm } as ViewStyle,
  paddingHorizontalMD: { paddingHorizontal: spacing.md } as ViewStyle,
  paddingHorizontalLG: { paddingHorizontal: spacing.lg } as ViewStyle,
  paddingHorizontalXL: { paddingHorizontal: spacing.xl } as ViewStyle,
  
  // Vertical padding
  paddingVerticalXS: { paddingVertical: spacing.xs } as ViewStyle,
  paddingVerticalSM: { paddingVertical: spacing.sm } as ViewStyle,
  paddingVerticalMD: { paddingVertical: spacing.md } as ViewStyle,
  paddingVerticalLG: { paddingVertical: spacing.lg } as ViewStyle,
  paddingVerticalXL: { paddingVertical: spacing.xl } as ViewStyle,
});

// Responsive button styles
export const responsiveButtons = StyleSheet.create({
  primary: {
    backgroundColor: '#007AFF',
    paddingHorizontal: responsiveSpacing(24),
    paddingVertical: responsiveSpacing(12),
    borderRadius: responsiveSpacing(8),
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: responsiveSpacing(44),
  } as ViewStyle,
  
  secondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#007AFF',
    paddingHorizontal: responsiveSpacing(24),
    paddingVertical: responsiveSpacing(12),
    borderRadius: responsiveSpacing(8),
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: responsiveSpacing(44),
  } as ViewStyle,
  
  tertiary: {
    backgroundColor: 'transparent',
    paddingHorizontal: responsiveSpacing(16),
    paddingVertical: responsiveSpacing(8),
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: responsiveSpacing(36),
  } as ViewStyle,
  
  fab: {
    position: 'absolute',
    bottom: responsiveSpacing(20),
    right: responsiveSpacing(20),
    width: responsiveSpacing(56),
    height: responsiveSpacing(56),
    borderRadius: responsiveSpacing(28),
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    ...platformValue({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
      },
      android: {
        elevation: 8,
      },
      web: {
        boxShadow: '0 4px 16px rgba(0,0,0,0.3)',
      },
      default: {},
    }),
  } as ViewStyle,
});

// Responsive input styles
export const responsiveInputs = StyleSheet.create({
  container: {
    marginBottom: responsiveSpacing(16),
  } as ViewStyle,
  
  label: {
    fontSize: responsiveFontSize(16),
    fontWeight: '600',
    color: '#333',
    marginBottom: responsiveSpacing(8),
  } as TextStyle,
  
  input: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: responsiveSpacing(8),
    paddingHorizontal: responsiveSpacing(12),
    paddingVertical: responsiveSpacing(12),
    fontSize: responsiveFontSize(16),
    minHeight: responsiveSpacing(44),
  } as ViewStyle,
  
  inputFocused: {
    borderColor: '#007AFF',
    borderWidth: 2,
  } as ViewStyle,
  
  inputError: {
    borderColor: '#FF3B30',
    borderWidth: 2,
  } as ViewStyle,
  
  errorText: {
    fontSize: responsiveFontSize(12),
    color: '#FF3B30',
    marginTop: responsiveSpacing(4),
  } as TextStyle,
  
  helperText: {
    fontSize: responsiveFontSize(12),
    color: '#666',
    marginTop: responsiveSpacing(4),
  } as TextStyle,
});

// Create device-specific styles
export const createDeviceSpecificStyles = <T extends Record<string, any>>(styles: {
  mobile?: T;
  tablet?: T;
  desktop?: T;
  default: T;
}): T => {
  const deviceType = getDeviceType();
  
  if (deviceType === 'desktop' && styles.desktop) {
    return { ...styles.default, ...styles.desktop };
  }
  
  if (deviceType === 'tablet' && styles.tablet) {
    return { ...styles.default, ...styles.tablet };
  }
  
  if (deviceType === 'mobile' && styles.mobile) {
    return { ...styles.default, ...styles.mobile };
  }
  
  return styles.default;
};

// Export commonly used responsive values
export const RESPONSIVE_VALUES = {
  borderRadius: {
    small: responsiveSpacing(4),
    medium: responsiveSpacing(8),
    large: responsiveSpacing(12),
    xlarge: responsiveSpacing(16),
  },
  
  iconSizes: {
    small: responsiveSpacing(16),
    medium: responsiveSpacing(24),
    large: responsiveSpacing(32),
    xlarge: responsiveSpacing(48),
  },
  
  minTouchTarget: responsiveSpacing(44),
  
  maxContentWidth: responsiveSpacing(1200),
};
