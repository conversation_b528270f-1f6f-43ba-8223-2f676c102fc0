{"name": "personal_manager", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "start:debug": "cross-env DEBUG_MODE=true expo start", "android": "expo start --android", "android:debug": "cross-env DEBUG_MODE=true expo start --android", "ios": "expo start --ios", "ios:debug": "cross-env DEBUG_MODE=true expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:debug": "DEBUG_TESTS=true jest --verbose", "test:unit": "jest --testPathPattern='(debug|test-helpers|simple)\\.test\\.(ts|tsx)$'", "test:integration": "jest --testPathPattern='integration\\.test\\.(ts|tsx)$'", "test:working": "jest --testPathPattern='(debug|test-helpers|simple|integration)\\.test\\.(ts|tsx)$'", "test:report": "node scripts/test-report.js", "build:web": "expo export --platform web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "deploy:web": "expo export --platform web && echo 'Upload dist folder to your hosting provider'", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/drawer": "^7.5.3", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.50.3", "expo": "~53.0.17", "expo-notifications": "^0.31.4", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-date-picker": "^5.0.13", "react-native-elements": "^3.4.3", "react-native-gesture-handler": "^2.27.1", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.18.0", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-svg": "^15.12.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "cross-env": "^7.0.3", "jest": "^30.0.4", "jest-expo": "^53.0.9", "typescript": "~5.8.3"}, "private": true}