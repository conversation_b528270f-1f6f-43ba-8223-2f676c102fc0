import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User as SupabaseUser } from '@supabase/supabase-js';
import { supabase } from '../../lib/supabase';
import { logger, DevTools, ErrorTracker, Assert } from '../utils/debug';
import { User } from '../types';

interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<{ error: any }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      if (session?.user) {
        fetchUserProfile(session.user);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        if (session?.user) {
          await fetchUserProfile(session.user);
        } else {
          setUser(null);
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchUserProfile = async (authUser: SupabaseUser) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', authUser.id)
        .single();

      if (error && error.code === 'PGRST116') {
        // Profile doesn't exist, create one
        const newProfile = {
          id: authUser.id,
          email: authUser.email!,
          full_name: authUser.user_metadata?.full_name || null,
          avatar_url: authUser.user_metadata?.avatar_url || null,
        };

        const { data: createdProfile, error: createError } = await supabase
          .from('profiles')
          .insert(newProfile)
          .select()
          .single();

        if (createError) {
          console.error('Error creating profile:', createError);
          logger.error('Database error saving new user', createError, {
            component: 'AuthContext',
            action: 'createProfile',
            metadata: { userId: authUser.id, email: authUser.email }
          });
        } else {
          console.log('Profile created successfully:', createdProfile);
          setUser(createdProfile);
        }
      } else if (error) {
        console.error('Error fetching profile:', error);
      } else {
        setUser(data);
      }
    } catch (error) {
      console.error('Error in fetchUserProfile:', error);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      Assert.isValidEmail(email);
      Assert.isNotNull(password, 'Password is required');

      logger.info('Attempting user sign in', { email }, { component: 'AuthContext' });
      DevTools.logUserAction('sign_in_attempt', { email });

      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        logger.error('Sign in failed', error, {
          component: 'AuthContext',
          action: 'signIn',
          metadata: { email }
        });
        DevTools.logUserAction('sign_in_failed', { email, error: error.message });
      } else {
        logger.info('Sign in successful', { email }, { component: 'AuthContext' });
        DevTools.logUserAction('sign_in_success', { email });
      }

      return { error };
    } catch (error) {
      logger.error('Unexpected error during sign in', error, {
        component: 'AuthContext',
        action: 'signIn',
        metadata: { email }
      });
      ErrorTracker.trackError(error as Error, {
        component: 'AuthContext',
        action: 'signIn'
      });
      return { error };
    }
  };

  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      logger.info('Starting sign up', { email, hasFullName: !!fullName }, { component: 'AuthContext' });
      DevTools.logUserAction('sign_up_attempt', { email });

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) {
        logger.error('Sign up failed', error, {
          component: 'AuthContext',
          action: 'signUp',
          metadata: { email, errorCode: error.code, errorMessage: error.message }
        });
        DevTools.logUserAction('sign_up_failed', { email, error: error.message });
        console.error('Detailed signup error:', {
          message: error.message,
          code: error.code,
          status: error.status,
          details: error
        });
      } else {
        logger.info('Sign up successful', {
          email,
          userId: data.user?.id,
          needsConfirmation: !data.session,
          hasSession: !!data.session,
          hasUser: !!data.user
        }, { component: 'AuthContext' });
        DevTools.logUserAction('sign_up_success', { email });
        console.log('Signup success details:', {
          userId: data.user?.id,
          email: data.user?.email,
          hasSession: !!data.session,
          userMetadata: data.user?.user_metadata
        });
      }

      return { error, data };
    } catch (error) {
      logger.error('Unexpected error during sign up', error, {
        component: 'AuthContext',
        action: 'signUp',
        metadata: { email }
      });
      ErrorTracker.trackError(error as Error, {
        component: 'AuthContext',
        action: 'signUp'
      });
      return { error };
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const updateProfile = async (updates: Partial<User>) => {
    if (!user) return { error: new Error('No user logged in') };

    const { error } = await supabase
      .from('profiles')
      .update(updates)
      .eq('id', user.id);

    if (!error) {
      setUser({ ...user, ...updates });
    }

    return { error };
  };

  const value: AuthContextType = {
    session,
    user,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
