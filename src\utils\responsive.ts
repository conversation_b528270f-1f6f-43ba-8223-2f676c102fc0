import { Dimensions, PixelRatio, Platform } from 'react-native';
import { useState, useEffect } from 'react';

// Get device dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Breakpoint constants based on common device sizes
export const BREAKPOINTS = {
  mobile: 0,
  tablet: 768,
  desktop: 1024,
  largeDesktop: 1440,
} as const;

// Device type detection
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

export const getDeviceType = (width: number = SCREEN_WIDTH): DeviceType => {
  if (width >= BREAKPOINTS.desktop) return 'desktop';
  if (width >= BREAKPOINTS.tablet) return 'tablet';
  return 'mobile';
};

// Screen dimension hook with real-time updates
export const useScreenDimensions = () => {
  const [dimensions, setDimensions] = useState(() => {
    const { width, height } = Dimensions.get('window');
    return {
      width,
      height,
      deviceType: getDeviceType(width),
      isLandscape: width > height,
      isPortrait: height > width,
    };
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setDimensions({
        width: window.width,
        height: window.height,
        deviceType: getDeviceType(window.width),
        isLandscape: window.width > window.height,
        isPortrait: window.height > window.width,
      });
    });

    return () => subscription?.remove();
  }, []);

  return dimensions;
};

// Responsive scaling functions
const guidelineBaseWidth = 375; // iPhone X width as baseline
const guidelineBaseHeight = 812; // iPhone X height as baseline

export const scale = (size: number): number => {
  return (SCREEN_WIDTH / guidelineBaseWidth) * size;
};

export const verticalScale = (size: number): number => {
  return (SCREEN_HEIGHT / guidelineBaseHeight) * size;
};

export const moderateScale = (size: number, factor: number = 0.5): number => {
  return size + (scale(size) - size) * factor;
};

// Responsive font scaling
export const responsiveFontSize = (size: number): number => {
  const deviceType = getDeviceType();
  const scaleFactor = PixelRatio.getFontScale();
  
  let adjustedSize = size;
  
  // Adjust base size for device type
  switch (deviceType) {
    case 'tablet':
      adjustedSize = size * 1.1;
      break;
    case 'desktop':
      adjustedSize = size * 1.2;
      break;
    default:
      adjustedSize = size;
  }
  
  return Math.round(adjustedSize * scaleFactor);
};

// Responsive spacing
export const spacing = {
  xs: moderateScale(4),
  sm: moderateScale(8),
  md: moderateScale(16),
  lg: moderateScale(24),
  xl: moderateScale(32),
  xxl: moderateScale(48),
};

// Responsive padding/margin helpers
export const responsiveSpacing = (baseSize: number): number => {
  const deviceType = getDeviceType();
  
  switch (deviceType) {
    case 'tablet':
      return baseSize * 1.25;
    case 'desktop':
      return baseSize * 1.5;
    default:
      return baseSize;
  }
};

// Grid system for responsive layouts
export const getGridColumns = (deviceType?: DeviceType): number => {
  const type = deviceType || getDeviceType();
  
  switch (type) {
    case 'desktop':
      return 4;
    case 'tablet':
      return 3;
    default:
      return 2;
  }
};

// Responsive width calculations
export const getResponsiveWidth = (columns: number, totalColumns?: number): string => {
  const total = totalColumns || getGridColumns();
  const percentage = (columns / total) * 100;
  return `${percentage}%`;
};

// Platform-specific responsive values
export const platformValue = <T>(values: {
  ios?: T;
  android?: T;
  web?: T;
  default: T;
}): T => {
  if (Platform.OS === 'ios' && values.ios !== undefined) return values.ios;
  if (Platform.OS === 'android' && values.android !== undefined) return values.android;
  if (Platform.OS === 'web' && values.web !== undefined) return values.web;
  return values.default;
};

// Responsive style helper
export const responsiveStyle = <T extends Record<string, any>>(styles: {
  mobile?: T;
  tablet?: T;
  desktop?: T;
  default: T;
}): T => {
  const deviceType = getDeviceType();
  
  if (deviceType === 'desktop' && styles.desktop) return styles.desktop;
  if (deviceType === 'tablet' && styles.tablet) return styles.tablet;
  if (deviceType === 'mobile' && styles.mobile) return styles.mobile;
  
  return styles.default;
};

// Safe area helpers for different devices
export const getSafeAreaPadding = () => {
  const deviceType = getDeviceType();
  const isLandscape = SCREEN_WIDTH > SCREEN_HEIGHT;
  
  return {
    paddingTop: platformValue({
      ios: deviceType === 'mobile' ? (isLandscape ? 0 : 44) : 0,
      default: 0,
    }),
    paddingBottom: platformValue({
      ios: deviceType === 'mobile' ? (isLandscape ? 21 : 34) : 0,
      default: 0,
    }),
  };
};

// Responsive modal sizing
export const getModalDimensions = () => {
  const deviceType = getDeviceType();
  const { width, height } = Dimensions.get('window');
  
  switch (deviceType) {
    case 'desktop':
      return {
        width: Math.min(600, width * 0.8),
        height: Math.min(800, height * 0.9),
        maxWidth: 600,
        maxHeight: 800,
      };
    case 'tablet':
      return {
        width: width * 0.85,
        height: height * 0.9,
        maxWidth: width * 0.85,
        maxHeight: height * 0.9,
      };
    default:
      return {
        width: width,
        height: height,
        maxWidth: width,
        maxHeight: height,
      };
  }
};

// Responsive navigation configuration
export const getNavigationConfig = () => {
  const deviceType = getDeviceType();
  const isLandscape = SCREEN_WIDTH > SCREEN_HEIGHT;
  
  return {
    showBottomTabs: deviceType === 'mobile' || (deviceType === 'tablet' && !isLandscape),
    showSideNavigation: deviceType === 'desktop' || (deviceType === 'tablet' && isLandscape),
    tabBarHeight: responsiveSpacing(60),
    sideNavWidth: responsiveSpacing(280),
  };
};

// Responsive card dimensions
export const getCardDimensions = () => {
  const deviceType = getDeviceType();
  const { width } = Dimensions.get('window');
  
  const padding = responsiveSpacing(16);
  const gap = responsiveSpacing(12);
  const columns = getGridColumns(deviceType);
  
  const availableWidth = width - (padding * 2);
  const cardWidth = (availableWidth - (gap * (columns - 1))) / columns;
  
  return {
    width: cardWidth,
    minWidth: cardWidth,
    maxWidth: cardWidth,
    padding: responsiveSpacing(16),
    margin: gap / 2,
  };
};

// Export commonly used values
export const RESPONSIVE_SCREEN = {
  width: SCREEN_WIDTH,
  height: SCREEN_HEIGHT,
  deviceType: getDeviceType(),
  isTablet: getDeviceType() === 'tablet',
  isDesktop: getDeviceType() === 'desktop',
  isMobile: getDeviceType() === 'mobile',
};
