import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFinancialData } from '../hooks/useFinancialData';
import AddAccountModal from '../components/AddAccountModal';
import { Account } from '../types';
import { useResponsive, useResponsiveTypography, useResponsiveSpacing } from '../hooks/useResponsive';
import { responsiveContainers, responsiveButtons } from '../utils/responsiveStyles';

const AccountsScreen: React.FC = () => {
  const { accounts, loading, fetchAccounts } = useFinancialData();
  const [showAddModal, setShowAddModal] = useState(false);

  // Responsive hooks
  const responsive = useResponsive();
  const typography = useResponsiveTypography();
  const spacing = useResponsiveSpacing();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getAccountIcon = (type: string) => {
    switch (type) {
      case 'checking':
        return 'card';
      case 'savings':
        return 'wallet';
      case 'credit':
        return 'card-outline';
      case 'investment':
        return 'trending-up';
      case 'cash':
        return 'cash';
      default:
        return 'card';
    }
  };

  const getAccountColor = (type: string) => {
    switch (type) {
      case 'checking':
        return '#4CAF50';
      case 'savings':
        return '#2196F3';
      case 'credit':
        return '#FF9800';
      case 'investment':
        return '#9C27B0';
      case 'cash':
        return '#607D8B';
      default:
        return '#666';
    }
  };

  const handleAddAccount = () => {
    setShowAddModal(true);
  };

  const handleAccountAdded = () => {
    fetchAccounts();
  };

  const renderAccount = ({ item }: { item: Account }) => (
    <TouchableOpacity style={styles.accountCard}>
      <View style={styles.accountHeader}>
        <View style={styles.accountInfo}>
          <Ionicons
            name={getAccountIcon(item.type)}
            size={24}
            color={getAccountColor(item.type)}
          />
          <View style={styles.accountDetails}>
            <Text style={styles.accountName}>{item.name}</Text>
            <Text style={styles.accountType}>
              {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
            </Text>
          </View>
        </View>
        <View style={styles.balanceContainer}>
          <Text
            style={[
              styles.balance,
              { color: item.balance >= 0 ? '#4CAF50' : '#F44336' },
            ]}
          >
            {formatCurrency(item.balance)}
          </Text>
          <Text style={styles.currency}>{item.currency}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const EmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="card-outline" size={64} color="#ccc" />
      <Text style={styles.emptyStateTitle}>No Accounts Yet</Text>
      <Text style={styles.emptyStateText}>
        Add your first account to start tracking your finances
      </Text>
      <TouchableOpacity style={styles.addButton} onPress={handleAddAccount}>
        <Ionicons name="add" size={24} color="white" />
        <Text style={styles.addButtonText}>Add Account</Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {accounts.length === 0 ? (
        <EmptyState />
      ) : (
        <>
          <FlatList
            data={accounts}
            renderItem={renderAccount}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContainer}
            refreshControl={
              <RefreshControl refreshing={loading} onRefresh={fetchAccounts} />
            }
          />
          <TouchableOpacity style={styles.fab} onPress={handleAddAccount}>
            <Ionicons name="add" size={24} color="white" />
          </TouchableOpacity>
        </>
      )}

      <AddAccountModal
        visible={showAddModal}
        onClose={() => setShowAddModal(false)}
        onAccountAdded={handleAccountAdded}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  ...responsiveContainers,
  listContainer: {
    padding: 16,
  },
  accountCard: {
    ...responsiveContainers.card,
  },
  accountHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  accountInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  accountDetails: {
    marginLeft: 12,
    flex: 1,
  },
  accountName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  accountType: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  balanceContainer: {
    alignItems: 'flex-end',
  },
  balance: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  currency: {
    fontSize: 12,
    color: '#666',
    marginTop: 2,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
  },
  emptyStateText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 32,
  },
  addButton: {
    backgroundColor: '#007AFF',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  addButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },
  fab: {
    ...responsiveButtons.fab,
  },
});

export default AccountsScreen;
